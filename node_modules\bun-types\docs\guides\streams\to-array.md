---
name: Convert a ReadableStream to an array of chunks
---

<PERSON><PERSON> provides a number of convenience functions for reading the contents of a [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream) into different formats. The `Bun.readableStreamToArray` function reads the contents of a `ReadableStream` to an array of chunks.

```ts
const stream = new ReadableStream();
const str = await Bun.readableStreamToArray(stream);
```

---

See [Docs > API > Utils](https://bun.sh/docs/api/utils#bun-readablestreamto) for documentation on <PERSON><PERSON>'s other `ReadableStream` conversion functions.
