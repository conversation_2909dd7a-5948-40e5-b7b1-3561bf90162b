---
name: Convert a Uint8Array to a Buffer
---

The [`<PERSON>uffer`](https://nodejs.org/api/buffer.html) class extends [`Uint8Array`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array) with a number of additional methods. Use `Buffer.from()` to create a `<PERSON>uffer` instance from a `Uint8Array`.

```ts
const arr: Uint8Array = ...
const buf = Buffer.from(arr);
```

---

See [Docs > API > Binary Data](https://bun.sh/docs/api/binary-data#conversion) for complete documentation on manipulating binary data with Bun.
