{"name": "connected-domain", "version": "1.0.0", "description": "calculate all the connected domains based on the given two-dimensional array", "main": "index.js", "scripts": {"test": "./node_modules/.bin/mocha  -t 0 -R spec test/spec.js"}, "repository": {"type": "git", "url": "git+https://github.com/neekey/connected-domain.git"}, "keywords": ["connected-domain"], "author": "neekey <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/neekey/connected-domain/issues"}, "homepage": "https://github.com/neekey/connected-domain#readme", "devDependencies": {"chai": "^3.5.0", "mocha": "^2.4.5"}}