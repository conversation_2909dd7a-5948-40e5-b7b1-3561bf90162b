---
name: Read a file as a string
---

The `Bun.file()` function accepts a path and returns a `BunFile` instance. The `BunFile` class extends `Blob` and allows you to lazily read the file in a variety of formats. Use `.text()` to read the contents as a string.

```ts
const path = "/path/to/file.txt";
const file = Bun.file(path);

const text = await file.text();
// string
```

---

Any relative paths will be resolved relative to the project root (the nearest directory containing a `package.json` file).

```ts
const path = "./file.txt";
const file = Bun.file(path);
```
