{"name": "rtsp-relay", "version": "1.9.0", "author": "<PERSON>", "license": "MIT", "description": "📽 Relay an RTSP stream through an existing express.js server", "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=10.0.0"}, "repository": "https://github.com/k-yle/rtsp-relay", "files": ["browser", "index.d.ts", "index.js"], "keywords": ["rtsp", "rtsp-stream", "stream", "websocket", "ws", "express", "ffmpeg", "jsmpeg"], "scripts": {"pretest": "chmod u+x test/rtsp-simple-server", "lint": "eslint --format pretty --ext .js,ts,.tsx --ignore-path .gitignore .", "test": "tsc && jest --runInBand", "build": "tsc index.js browser/index.js --declaration --allowJs --emitDeclarationOnly --resolveJsonModule --skipLibCheck", "trypublish": "npm publish --provenance || true"}, "dependencies": {"express-ws": "^5.0.2", "ffmpeg-static": "^5.2.0", "ps-node": "^0.1.6"}, "devDependencies": {"@types/express-ws": "^3.0.4", "@types/ffmpeg-static": "^3.0.3", "@types/jest": "^29.5.12", "@types/jest-image-snapshot": "^6.4.0", "@types/node": "^20.12.7", "@types/ps-node": "^0.1.3", "@types/puppeteer": "^7.0.4", "eslint": "^8.57.0", "eslint-config-kyle": "^9.42.0", "express": "^4.19.2", "jest": "^29.7.0", "jest-image-snapshot": "^6.4.0", "puppeteer": "^22.10.0", "typescript": "^5.4.5"}, "eslintConfig": {"rules": {"logical-assignment-operators": 0}, "extends": "kyle"}, "prettier": "eslint-config-kyle/prettier", "jest": {"forceExit": true, "testTimeout": 20000, "setupFiles": ["./test/setupTests.js"], "reporters": ["default", "jest-image-snapshot/src/outdated-snapshot-reporter.js"], "collectCoverage": true}}