{"name": "express-ws", "version": "5.0.2", "description": "WebSocket endpoints for Express applications", "main": "index.js", "scripts": {"prepublish": "npm run build", "build": "babel src/ -d lib/", "lint": "eslint src/"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"ws": "^7.4.6"}, "peerDependencies": {"express": "^4.0.0 || ^5.0.0-alpha.1"}, "engines": {"node": ">=4.5.0"}, "directories": {"example": "examples"}, "repository": {"type": "git", "url": "https://github.com/HenningM/express-ws"}, "keywords": ["express", "ws", "websocket"], "bugs": {"url": "https://github.com/HenningM/express-ws/issues"}, "homepage": "https://github.com/HenningM/express-ws", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.5.0", "eslint": "^7.27.0", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.12.0", "express": "^5.0.0-alpha.6"}}