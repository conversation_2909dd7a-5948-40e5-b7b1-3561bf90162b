To automatically re-run tests when files change, use the `--watch` flag:

```sh
$ bun test --watch
```

<PERSON><PERSON> will watch for changes to any files imported in a test file, and re-run tests when a change is detected.

It's fast.

{% raw %}

<blockquote class="twitter-tweet"><p lang="en" dir="ltr">&quot;bun test --watch url&quot; in a large folder with multiple files that start with &quot;url&quot; <a href="https://t.co/aZV9BP4eFu">pic.twitter.com/aZV9BP4eFu</a></p>&mdash; <PERSON><PERSON><PERSON> (@jarredsumner) <a href="https://twitter.com/jarredsumner/status/1640890850535436288?ref_src=twsrc%5Etfw">March 29, 2023</a></blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

{% /raw %}
