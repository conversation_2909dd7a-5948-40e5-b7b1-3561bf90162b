environment:
  matrix:
    - nodejs_version: "7"
    - nodejs_version: '6'
    - nodejs_version: '4'
    - nodejs_version: '0.12'
install:
  - ps: Install-Product node $env:nodejs_version
  - set CI=true
  - npm -g install npm@latest
  - set PATH=%APPDATA%\npm;%PATH%
  - npm install
matrix:
  fast_finish: true
build: off
shallow_clone: true
test_script:
  - node --version
  - npm --version
  - npm test
