---
name: Import a TOML file
---

B<PERSON> natively supports importing `.toml` files.

```toml#data.toml
name = "bun"
version = "1.0.0"

[author]
name = "<PERSON>"
email = "<EMAIL>"
```

---

Import the file like any other source file.

```ts
import data from "./data.toml";

data.name; // => "bun"
data.version; // => "1.0.0"
data.author.name; // => "<PERSON>"
```

---

See [Docs > Runtime > TypeScript](https://bun.sh/docs/runtime/typescript) for more information on using TypeScript with Bun.
