{"name": "@types/bun", "version": "1.2.14", "description": "TypeScript definitions for bun", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bun", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Jar<PERSON>-<PERSON>"}, {"name": "Ashcon <PERSON>ovi", "githubUsername": "electroid", "url": "https://github.com/electroid"}, {"name": "<PERSON>", "githubUsername": "paperclover", "url": "https://github.com/paperclover"}, {"name": "Rob<PERSON>un", "githubUsername": "robobun", "url": "https://github.com/robobun"}, {"name": "<PERSON>", "githubUsername": "dylan-conway", "url": "https://github.com/dylan-conway"}, {"name": "<PERSON><PERSON>", "githubUsername": "nektro", "url": "https://github.com/nektro"}, {"name": "<PERSON>", "githubUsername": "RiskyMH", "url": "https://github.com/RiskyMH"}, {"name": "<PERSON>", "githubUsername": "alii", "url": "https://github.com/alii"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bun"}, "scripts": {}, "dependencies": {"bun-types": "1.2.14"}, "peerDependencies": {}, "typesPublisherContentHash": "bb99f2104ff72aa27cdfc700d078274c98bd2268646de34019a2ebf77ce85586", "typeScriptVersion": "5.1"}